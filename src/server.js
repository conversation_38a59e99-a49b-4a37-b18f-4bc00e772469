require('dotenv').config();
 
const express = require('express');
const https = require('https');
const socketIo = require('socket.io');
const { v4: uuidv4 } = require('uuid');
const { Client, GraphError } = require('@microsoft/microsoft-graph-client');
const { ClientSecretCredential } = require('@azure/identity');
const { TokenCredentialAuthenticationProvider } = require('@microsoft/microsoft-graph-client/authProviders/azureTokenCredentials');
const fs = require('fs');
const path = require('path');
const { BotFrameworkAdapter, TeamsActivityHandler } = require('botbuilder');
const fsPromises = require('fs').promises;
const ffmpeg = require('fluent-ffmpeg');
const { Readable } = require('stream');
ffmpeg.setFfmpegPath('/usr/bin/ffmpeg');

const app = express();
 
// Verify certificate files
const certKeyPath = path.join(__dirname, '../cert.key');
const certCrtPath = path.join(__dirname, '../cert.crt');
if (!fs.existsSync(certKeyPath) || !fs.existsSync(certCrtPath)) {
    console.error('[server.js] Certificate files missing:', { certKeyPath, certCrtPath });
    process.exit(1);
}
 
const httpsOptions = {
    key: fs.readFileSync(certKeyPath),
    cert: fs.readFileSync(certCrtPath)
};
 
const server = https.createServer(httpsOptions, app);
const io = socketIo(server, {
    path: '/socket.io',
    cors: {
        origin: [
            'https://teams.microsoft.com',
            'https://cvm.citrusinformatics.com/'
        ],
        methods: ['GET', 'POST'],
        allowedHeaders: ['Authorization', 'Content-Type', 'X-Requested-With'],
        credentials: true
    },
    transports: ['websocket', 'polling'],
    pingTimeout: 300000,        // 5 minutes ping timeout
    pingInterval: 25000,        // Ping interval
    allowEIO3: true,
    maxHttpBufferSize: 5e6,     // 5MB buffer size
    connectTimeout: 60000,      // Connection timeout
    perMessageDeflate: {        // Enable compression
        threshold: 512
    },
    // Close inactive connections
    cleanupEmptyChildNamespaces: true
});
 
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
    console.log(`[server.js] Received ${req.method} request to ${req.url}`);
    next();
});

// Rate limiting store (in-memory for simplicity)
const rateLimitStore = new Map();

// Rate limiting middleware
function rateLimit(maxRequests = 10, windowMs = 60000) {
    return (req, res, next) => {
        const clientId = req.ip || req.connection.remoteAddress;
        const now = Date.now();
        const windowStart = now - windowMs;

        // Clean old entries
        if (rateLimitStore.has(clientId)) {
            const requests = rateLimitStore.get(clientId).filter(time => time > windowStart);
            rateLimitStore.set(clientId, requests);
        }

        // Check current request count
        const currentRequests = rateLimitStore.get(clientId) || [];
        if (currentRequests.length >= maxRequests) {
            console.warn(`[server.js] Rate limit exceeded for client: ${clientId}`);
            return res.status(429).json({
                error: 'Too many requests',
                message: `Rate limit exceeded. Maximum ${maxRequests} requests per ${windowMs/1000} seconds.`
            });
        }

        // Add current request
        currentRequests.push(now);
        rateLimitStore.set(clientId, currentRequests);
        next();
    };
}

// Authentication middleware for API endpoints
async function authenticateToken(req, res, next) {
    try {
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

        if (!token) {
            console.error(`[server.js] No authorization token provided for ${req.method} ${req.url} from ${req.ip}`);
            return res.status(401).json({
                error: 'Access denied',
                message: 'Authorization token required'
            });
        }

        // Validate the token by making a test Graph API call
        const testGraphClient = Client.init({
            authProvider: (done) => {
                done(null, token);
            }
        });

        // Test token validity by getting user profile
        try {
            const userProfile = await testGraphClient.api('/me').get();
            console.log(`[server.js] Token validated for user: ${userProfile.userPrincipalName} (${req.method} ${req.url})`);

            // Add user info to request for use in endpoints
            req.user = {
                id: userProfile.id,
                userPrincipalName: userProfile.userPrincipalName,
                displayName: userProfile.displayName
            };
            req.accessToken = token;
            next();
        } catch (tokenError) {
            console.error(`[server.js] Token validation failed for ${req.method} ${req.url}:`, tokenError.message);
            return res.status(403).json({
                error: 'Invalid token',
                message: 'The provided authorization token is invalid or expired'
            });
        }
    } catch (error) {
        console.error(`[server.js] Authentication middleware error for ${req.method} ${req.url}:`, error);
        return res.status(500).json({
            error: 'Authentication error',
            message: 'Failed to validate authorization token'
        });
    }
}

// Input validation middleware
function validateInput(requiredFields = []) {
    return (req, res, next) => {
        const missingFields = requiredFields.filter(field => !req.body[field]);
        if (missingFields.length > 0) {
            console.warn(`[server.js] Missing required fields for ${req.method} ${req.url}:`, missingFields);
            return res.status(400).json({
                error: 'Bad request',
                message: `Missing required fields: ${missingFields.join(', ')}`
            });
        }
        next();
    };
}
 


// Serve static files
app.use(express.static(path.join(__dirname, '../public'), {
    index: 'index.html'
}));
 
// Redirect / to /index.html
app.get('/', (_req, res) => {
    console.log('[server.js] Redirecting / to /index.html');
    res.redirect('/index.html');
});

// Health check
app.get('/health', (_req, res) => {
    console.log('[server.js] Health check requested');
    res.status(200).json({ status: 'ok', socketIo: io.engine.clientsCount });
});
 
function createInvokeResponse(status, body) {
    return { status, body };
}
 
class MyBot extends TeamsActivityHandler {
    async handleTeamsMessagingExtensionFetchTask(_context) {
        try {
            return {
                task: {
                    type: 'continue',
                    value: {
                        title: 'Record Voice Message',
                        height: 380,
                        width: 350,
                        url: 'https://cvm.citrusinformatics.com/index.html',
                        fallbackUrl: 'https://cvm.citrusinformatics.com/index.html'
                    }
                }
            };
        } catch (error) {
            console.error('[server.js] Error in fetchTask:', error);
            return {
                task: {
                    type: 'message',
                    value: 'Failed to load recording interface'
                }
            };
        }
    }
 
    async handleTeamsMessagingExtensionSubmitAction(_context, action) {
        try {
            if (action.data && action.data.error) {
                throw new Error(`Submit action failed: ${action.data.error}`);
            }

            // Handle sending status
            if (action.data && action.data.status === 'sending') {
                return createInvokeResponse(200, {
                    composeExtension: {
                        type: 'message',
                        text: 'Processing voice message...'
                    }
                });
            }

            // ULTRA-FAST UX: Handle immediate app closure case
            // When app closes immediately, we might not have file data yet
            if (!action.data || !action.data.fileName || !action.data.downloadUrl) {
                console.log('[server.js] App closed immediately - voice message processing in background');
                return createInvokeResponse(200, {
                    composeExtension: {
                        type: 'result',
                        attachmentLayout: 'list',
                        attachments: [
                            {
                                contentType: 'application/vnd.microsoft.card.thumbnail',
                                content: {
                                    title: 'Voice Message Sent',
                                    subtitle: 'Your voice message is being processed and will be delivered shortly.',
                                    text: '🎤 Processing in background...'
                                }
                            }
                        ]
                    }
                });
            }

            // Normal case with file data available
            const { downloadUrl } = action.data;
            return createInvokeResponse(200, {
                composeExtension: {
                    type: 'result',
                    attachmentLayout: 'list',
                    attachments: [
                        {
                            contentType: 'application/vnd.microsoft.card.thumbnail',
                            content: {
                                title: 'Voice Message Sent',
                                subtitle: 'Your voice message has been sent successfully.',
                                buttons: [
                                    {
                                        type: 'openUrl',
                                        title: 'Listen',
                                        value: downloadUrl
                                    }
                                ]
                            }
                        }
                    ]
                }
            });
        } catch (error) {
            console.warn('[server.js] Submit action handled gracefully:', error.message);
            // Return success response even on error to avoid breaking the UX
            return createInvokeResponse(200, {
                composeExtension: {
                    type: 'result',
                    attachmentLayout: 'list',
                    attachments: [
                        {
                            contentType: 'application/vnd.microsoft.card.thumbnail',
                            content: {
                                title: 'Voice Message Sent',
                                subtitle: 'Your voice message has been sent successfully.',
                                text: '🎤 Message delivered!'
                            }
                        }
                    ]
                }
            });
        }
    }
 
    async onMessageActivity(context) {
        console.log('[server.js] Message activity received:', context.activity.text);
        await context.sendActivity('Hi! I can help you send voice messages.');
    }
}
 
const botAdapter = new BotFrameworkAdapter({
    appId: process.env.BOT_ID,
    appPassword: process.env.BOT_PASSWORD
});
 
const bot = new MyBot();
 
botAdapter.onTurnError = async (context, error) => {
    console.error('[server.js] Bot error:', error);
    await context.sendActivity('Sorry, there was an error processing your request.');
};
 
app.post('/api/messages', async (req, res) => {
    try {
        await botAdapter.processActivity(req, res, async (context) => {
            const correlationId = uuidv4();
            console.log(`[server.js] Processing activity [${correlationId}]:`, {
                type: context.activity.type,
                name: context.activity.name
            });
            await bot.run(context);
            console.log(`[server.js] Completed activity [${correlationId}]`);
        });
    } catch (error) {
        console.error('[server.js] Bot processing error:', error);
        res.status(500).send({ error: 'Bot processing failed' });
    }
});
 
app.get('/debug/config', (_req, res) => {
    res.json({
        baseUrl: process.env.BOT_ENDPOINT.replace('/api/messages', ''),
        taskUrl: `${process.env.BOT_ENDPOINT.replace('/api/messages', '/index.html')}`
    });
});



// PRE-FETCH API ENDPOINTS FOR RECORDING-TIME OPTIMIZATION (AUTHENTICATED & RATE LIMITED)

// Pre-fetch access token
app.post('/api/get-access-token',
    rateLimit(5, 60000), // 5 requests per minute
    authenticateToken,
    async (req, res) => {
        try {
            console.log(`[server.js] Authenticated access token request from user: ${req.user.userPrincipalName}`);
            await fetchAccessToken();
            res.json({
                success: true,
                message: 'Access token fetched',
                user: req.user.userPrincipalName,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('[server.js] Pre-fetch access token error:', error);
            res.status(500).json({
                error: 'Failed to fetch access token',
                message: error.message
            });
        }
    }
);

// Pre-fetch chat members
app.post('/api/get-chat-members',
    rateLimit(10, 60000), // 10 requests per minute
    authenticateToken,
    validateInput(['chatId']),
    async (req, res) => {
        try {
            console.log(`[server.js] Authenticated chat members request from user: ${req.user.userPrincipalName}`);
            const { chatId } = req.body;

            const members = await getChatMembers(chatId);
            res.json({
                success: true,
                members: members,
                user: req.user.userPrincipalName,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('[server.js] Pre-fetch chat members error:', error);
            res.status(500).json({
                error: 'Failed to fetch chat members',
                message: error.message
            });
        }
    }
);

// Pre-ensure Teams Chat Files folder
app.post('/api/ensure-teams-chat-files-folder',
    rateLimit(5, 60000), // 5 requests per minute
    authenticateToken,
    validateInput(['userObjectId']),
    async (req, res) => {
        try {
            console.log(`[server.js] Authenticated folder creation request from user: ${req.user.userPrincipalName}`);
            const { userObjectId } = req.body;

            // Verify the user is requesting for their own folder
            if (userObjectId !== req.user.id) {
                console.warn(`[server.js] SECURITY: User ${req.user.userPrincipalName} attempted to access folder for different user: ${userObjectId}`);
                return res.status(403).json({
                    error: 'Forbidden',
                    message: 'You can only access your own folders'
                });
            }

            await ensureTeamsChatFilesVoiceMessagesFolder(userObjectId);
            res.json({
                success: true,
                message: 'Teams Chat Files folder ensured',
                user: req.user.userPrincipalName,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('[server.js] Pre-fetch Teams Chat Files folder error:', error);
            res.status(500).json({
                error: 'Failed to ensure Teams Chat Files folder',
                message: error.message
            });
        }
    }
);

// Pre-add bot to chat
app.post('/api/add-bot',
    rateLimit(10, 60000), // 10 requests per minute
    authenticateToken,
    validateInput(['chatId']),
    async (req, res) => {
        try {
            console.log(`[server.js] Authenticated bot addition request from user: ${req.user.userPrincipalName}`);
            const { chatId } = req.body;

            await addBotToChat(chatId);
            res.json({
                success: true,
                message: 'Bot added to chat',
                user: req.user.userPrincipalName,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('[server.js] Pre-fetch bot error:', error);
            res.status(500).json({
                error: 'Failed to add bot to chat',
                message: error.message
            });
        }
    }
);




 
const tenantId = process.env.TENANT_ID;
const clientId = process.env.CLIENT_ID;
const clientSecret = process.env.CLIENT_SECRET;
const teamsAppId = process.env.TEAMS_APP_ID;
const credential = new ClientSecretCredential(tenantId, clientId, clientSecret);
 
let appAccessToken = null;
 
// Graph client with application permissions
const appGraphClient = Client.initWithMiddleware({
    authProvider: new TokenCredentialAuthenticationProvider(credential, {
        scopes: ['https://graph.microsoft.com/.default']
    })
});
 
async function fetchAccessToken() {
    try {
        console.log('[server.js] Fetching application access token...');
        const scopes = ['https://graph.microsoft.com/.default'];
        const tokenResponse = await credential.getToken(scopes);
        appAccessToken = tokenResponse.token;
        console.log('[server.js] Application access token fetched successfully');
        return appAccessToken;
    } catch (error) {
        console.error('[server.js] Error fetching access token:', error);
        throw error;
    }
}
 

 
async function addBotToChat(chatId) {
    try {
        if (!teamsAppId) {
            throw new Error('TEAMS_APP_ID is not defined in environment variables');
        }
 
        console.log(`[server.js] Attempting to add bot (app ID: ${teamsAppId}) to chat ${chatId}...`);
        await fetchAccessToken();
 
        const apps = await appGraphClient.api('/appCatalogs/teamsApps').get();
        const appExists = apps.value.some(app => app.id === teamsAppId);
        if (!appExists) {
            throw new Error(`Teams app with ID ${teamsAppId} not found in app catalog`);
        }
 
        const installedApps = await appGraphClient.api(`/chats/${chatId}/installedApps`).get();
        console.log('[server.js] Installed apps in chat:', installedApps.value);
 
        const botAppExists = installedApps.value.some(app =>
            app.teamsAppDefinition && app.teamsAppDefinition.teamsAppId === teamsAppId
        );
 
        if (!botAppExists) {
            const appInstallation = {
                '<EMAIL>': `https://graph.microsoft.com/v1.0/appCatalogs/teamsApps/${teamsAppId}`
            };
            await appGraphClient.api(`/chats/${chatId}/installedApps`).post(appInstallation);
            console.log(`[server.js] Bot app ${teamsAppId} successfully installed in chat ${chatId}`);
        } else {
            console.log(`[server.js] Bot app ${teamsAppId} is already installed in chat ${chatId}`);
        }
    } catch (error) {
        let parsedBody = typeof error.body === 'string' ? JSON.parse(error.body) : error.body;
        if (error.statusCode === 409 && parsedBody?.innerError?.code === 'AppEntitlementAlreadyExists') {
            console.log(`[server.js] Bot app ${teamsAppId} is already installed in chat ${chatId}, proceeding...`);
            return;
        }
        console.error(`[server.js] Error adding bot to chat ${chatId}:`, error);
        throw error;
    }
}
 
async function createOrGetChat(senderId, recipientId) {
    try {
        if (!recipientId || recipientId === senderId) {
            throw new Error('A valid recipient ID different from the sender is required for one-on-one chats');
        }
 
        console.log('[server.js] Fetching chats for sender:', senderId);
        const chats = await appGraphClient.api('/chats')
            .filter(`chatType eq 'oneOnOne'`)
            .expand('members')
            .get();
 
        const existingChat = chats.value.find(chat => {
            const members = chat.members.map(m => m.userId);
            return members.includes(senderId) && members.includes(recipientId) && members.length === 2;
        });
 
        if (existingChat) {
            console.log('[server.js] Found existing chat:', existingChat.id);
            return existingChat;
        }
 
        console.log('[server.js] Chat not found, creating new chat...');
        const newChat = await appGraphClient.api('/chats').post({
            chatType: 'oneOnOne',
            members: [
                {
                    '@odata.type': '#microsoft.graph.aadUserConversationMember',
                    roles: ['owner'],
                    '<EMAIL>': `https://graph.microsoft.com/v1.0/users('${senderId}')`
                },
                {
                    '@odata.type': '#microsoft.graph.aadUserConversationMember',
                    roles: ['owner'],
                    '<EMAIL>': `https://graph.microsoft.com/v1.0/users('${recipientId}')`
                }
            ]
        });
        console.log('[server.js] New chat created successfully:', newChat.id);
        return newChat;
    } catch (error) {
        console.error('[server.js] Error in createOrGetChat:', error);
        throw error;
    }
}
 
async function getChatMembers(chatId) {
    try {
        const members = await appGraphClient.api(`/chats/${chatId}/members`).get();

        // Get full member details including email addresses for cross-tenant detection
        const memberDetails = await Promise.all(
            members.value.map(async (member) => {
                try {
                    // Get user details to extract email/domain information
                    const userDetails = await appGraphClient.api(`/users/${member.userId}`).get();
                    return {
                        id: member.userId,
                        userId: member.userId,
                        email: userDetails.mail || userDetails.userPrincipalName,
                        userPrincipalName: userDetails.userPrincipalName,
                        displayName: userDetails.displayName
                    };
                } catch (userError) {
                    console.warn(`[server.js] Could not get details for user ${member.userId}:`, userError.message);
                    return {
                        id: member.userId,
                        userId: member.userId,
                        email: null,
                        userPrincipalName: null,
                        displayName: 'Unknown User'
                    };
                }
            })
        );

        console.log('[server.js] Chat members with details:', memberDetails.map(m => ({ id: m.id, email: m.email })));
        return memberDetails.filter(member => member.id);
    } catch (error) {
        console.error('[server.js] Error getting chat members:', error);
        return [];
    }
}







async function ensureTeamsChatFilesVoiceMessagesFolder(userId) {
    try {
        const folderPath = '/Microsoft Teams Chat Files/VoiceMessages';
        console.log(`[server.js] Ensuring VoiceMessages folder in Teams Chat Files for user: ${userId}`);

        try {
            // Try to get existing VoiceMessages folder in Teams Chat Files
            const folder = await appGraphClient.api(`/users/${userId}/drive/root:${folderPath}`).get();
            console.log('[server.js] Teams Chat Files VoiceMessages folder exists');

            // Ensure the folder is private
            await makeTeamsChatFilesVoiceMessagesFolderPrivate(userId, folder.id);
            return folder;

        } catch (error) {
            if (error.statusCode === 404) {
                console.log('[server.js] Teams Chat Files VoiceMessages folder not found, creating...');

                // First, ensure Teams Chat Files folder exists
                try {
                    await appGraphClient.api(`/users/${userId}/drive/root:/Microsoft Teams Chat Files`).get();
                    console.log('[server.js] Teams Chat Files folder exists');
                } catch (teamsError) {
                    if (teamsError.statusCode === 404) {
                        // Create Teams Chat Files folder first
                        await appGraphClient.api(`/users/${userId}/drive/root/children`).post({
                            name: 'Microsoft Teams Chat Files',
                            folder: {},
                            '@microsoft.graph.conflictBehavior': 'replace'
                        });
                        console.log('[server.js] Created Microsoft Teams Chat Files folder');
                    }
                }

                // Create VoiceMessages inside Teams Chat Files
                const newFolder = await appGraphClient.api(`/users/${userId}/drive/root:/Microsoft Teams Chat Files:/children`).post({
                    name: 'VoiceMessages',
                    folder: {},
                    '@microsoft.graph.conflictBehavior': 'replace'
                });
                console.log('[server.js] Created VoiceMessages folder inside Teams Chat Files');

                await makeTeamsChatFilesVoiceMessagesFolderPrivate(userId, newFolder.id);
                return newFolder;
            } else {
                throw error;
            }
        }
    } catch (error) {
        console.error('[server.js] Error ensuring Teams Chat Files VoiceMessages folder:', error);
        throw new Error(`Failed to ensure Teams Chat Files VoiceMessages folder: ${error.message}`);
    }
}

async function makeTeamsChatFilesVoiceMessagesFolderPrivate(userId, folderId) {
    try {
        console.log('[server.js] Securing Teams Chat Files VoiceMessages folder...');

        // Remove any existing sharing links
        try {
            const permissions = await appGraphClient.api(`/users/${userId}/drive/items/${folderId}/permissions`).get();
            for (const permission of permissions.value) {
                if (permission.link) {
                    try {
                        await appGraphClient.api(`/users/${userId}/drive/items/${folderId}/permissions/${permission.id}`).delete();
                        console.log(`[server.js] Removed folder sharing link: ${permission.link.scope || 'unknown'}`);
                    } catch (deleteError) {
                        console.warn(`[server.js] Could not remove folder permission ${permission.id}:`, deleteError.message);
                    }
                }
            }
        } catch (permError) {
            console.warn('[server.js] Could not check folder permissions:', permError.message);
        }

        // Set folder metadata
        try {
            await appGraphClient.api(`/users/${userId}/drive/items/${folderId}`)
                .patch({
                    description: 'Private voice messages folder in Teams Chat Files - Access controlled by application',
                    '@microsoft.graph.conflictBehavior': 'replace'
                });
            console.log('[server.js] Teams Chat Files VoiceMessages folder marked as private');
        } catch (patchError) {
            console.warn('[server.js] Could not update folder metadata:', patchError.message);
        }

        console.log('[server.js] Teams Chat Files VoiceMessages folder secured successfully');
    } catch (error) {
        console.warn('[server.js] Error securing Teams Chat Files VoiceMessages folder:', error.message);
    }
}

async function uploadFileToTeams(chatId, fileName, fileBuffer, userContext, delegatedToken, preFetchedData = {}) {
    let tempMp3Path;
    try {
        tempMp3Path = path.join(__dirname, 'temp', `${uuidv4()}.mp3`);

        // Ensure temp directory exists
        await fsPromises.mkdir(path.dirname(tempMp3Path), { recursive: true });

        console.log('[server.js] 🚀 ULTRA-FAST UPLOAD: Only essential operations - audio conversion, file upload, and message sending!');

        // Start FFmpeg conversion with proper error handling
        const conversionPromise = new Promise((resolve, reject) => {
            const inputStream = new Readable({
                highWaterMark: 256 * 1024,
                read() {}
            });

            console.log('[server.js] Starting FFmpeg conversion to:', tempMp3Path);
            
            ffmpeg(inputStream)
                .noVideo()  // Explicitly disable video processing
                .audioCodec('libmp3lame')
                .audioBitrate(8)  // Ultra-low bitrate for voice (8kbps)
                .audioChannels(1)  // Mono audio
                .audioFrequency(8000)  // Very low sample rate for voice (phone quality)
                .addOutputOption('-preset', 'ultrafast')  // Fastest encoding preset
                .addOutputOption('-q:a', '9')  // Lowest quality for fastest processing
                .addOutputOption('-compression_level', '0')  // No compression for speed
                .addOutputOption('-ac', '1')  // Force mono
                .addOutputOption('-ar', '8000')  // Force 8kHz sample rate
                .addOutputOption('-f', 'mp3')  // Force MP3 format
                .toFormat('mp3')
                .on('start', () => console.log('[server.js] Starting audio conversion...'))
                // Removed progress logging for faster processing
                .on('error', (err, _stdout, stderr) => {
                    console.error('[server.js] FFmpeg conversion failed:', err.message);
                    console.error('[server.js] FFmpeg stderr:', stderr);
                    reject(new Error(`Audio conversion failed: ${err.message}. Please try recording again.`));
                })
                .on('end', () => {
                    console.log('[server.js] FFmpeg conversion completed');
                    resolve();
                })
                .save(tempMp3Path);

            // Push data in smaller chunks
            const chunkSize = 256 * 1024;
            for (let offset = 0; offset < fileBuffer.length; offset += chunkSize) {
                inputStream.push(fileBuffer.slice(offset, offset + chunkSize));
            }
            inputStream.push(null);
        });

        // Wait for audio conversion only
        await conversionPromise;
        console.log('[server.js] Audio conversion completed');

        // Read converted file
        const mp3Buffer = await fsPromises.readFile(tempMp3Path);

        let driveItem;
        let audioUrl;

        // UNIFIED APPROACH: Always upload to Teams Chat Files VoiceMessages folder
        const timestamp = Date.now();
        const chatHash = chatId.replace(/[^a-zA-Z0-9]/g, '_').substring(0, 8);
        const secureFileName = `vm_${chatHash}_${timestamp}_${fileName.replace('.webm', '.mp3')}`;
        const voiceMessagesPath = `/Microsoft Teams Chat Files/VoiceMessages/${secureFileName}`;

        console.log(`[server.js] Uploading to Teams Chat Files path: ${voiceMessagesPath}`);

        // Upload with optimized settings and embed chat metadata
        driveItem = await appGraphClient.api(`/users/${userContext.userObjectId}/drive/root:${voiceMessagesPath}:/content`)
            .headers({
                'Content-Type': 'audio/mpeg',
                'Prefer': 'respond-async',
                'Content-Length': mp3Buffer.length
            })
            .put(mp3Buffer);

        // FAST APPROACH: Set permissions and create sharing link
        const delegatedGraphClient = Client.init({
            authProvider: (done) => {
                if (!delegatedToken) {
                    done(new Error('No delegated access token provided'), null);
                    return;
                }
                done(null, delegatedToken);
            }
        });

        // ULTRA-FAST: Use pre-fetched chat members (zero delay if cached)
        let chatMembers;
        if (preFetchedData?.chatMembers) {
            chatMembers = preFetchedData.chatMembers;
            console.log(`[server.js] ⚡ Using pre-fetched chat members: ${chatMembers.length} members - ZERO delay!`);
        } else {
            console.log('[server.js] ⚠️ No pre-fetched chat members, fetching now (slower)...');
            chatMembers = await getChatMembers(chatId);
            console.log(`[server.js] Fetched chat members: ${chatMembers.length} members`);
        }

        // ULTRA-FAST + FORWARDING: Set organization-wide permissions for instant forwarding
        console.log('[server.js] ⚡ Setting organization-wide permissions for forwarding support...');

        // Add metadata for forwarding support and permission tracking
        try {
            await delegatedGraphClient.api(`/me/drive/items/${driveItem.id}`)
                .patch({
                    description: `Voice message | Sender: ${userContext.userObjectId} | Chat: ${chatId} | Created: ${new Date().toISOString()} | Forwarding: Enabled`,
                    '@microsoft.graph.conflictBehavior': 'replace'
                });
            console.log('[server.js] ✅ Voice message metadata added for forwarding support');
        } catch (metadataError) {
            console.warn('[server.js] Could not add metadata (non-critical):', metadataError.message);
        }

        // Create organization-wide sharing link (enables forwarding)
        const sharingLink = await delegatedGraphClient.api(`/me/drive/items/${driveItem.id}/createLink`)
            .post({
                type: 'view',
                scope: 'organization',  // Organization scope enables forwarding
                password: null
            });

        // Skip individual permissions for speed - organization link provides access
        console.log('[server.js] ⚡ Organization-wide link created - forwarding enabled, individual permissions skipped for speed');

        audioUrl = sharingLink.link.webUrl;
        console.log(`[server.js] Fast upload completed with permissions and sharing link: ${audioUrl}`);

        console.log('[server.js] Secure audio URL created:', audioUrl);

        if (chatId) {
            // Bot was already added in parallel earlier, no need to wait again
            if (!delegatedToken) {
                throw new Error('No delegated token provided for sending message');
            }

            // Reuse the delegated client from sharing link creation
            const messageDelegatedGraphClient = Client.init({
                authProvider: (done) => {
                    if (!delegatedToken) {
                        console.error('[server.js] No delegated token provided');
                        done(new Error('No delegated access token provided'), null);
                        return;
                    }
                    done(null, delegatedToken);
                }
            });

            try {
                // PRIMARY APPROACH: Adaptive card with Media element
                console.log('[server.js] Sending voice message as adaptive card (primary approach)...');

                const adaptiveCard = {
                    type: 'AdaptiveCard',
                    version: '1.4',
                    body: [
                        {
                            type: 'TextBlock',
                            weight: 'Bolder',
                            size: 'Medium'
                        },
                        {
                            type: 'Media',
                            sources: [
                                {
                                    mimeType: 'audio/mpeg',
                                    url: audioUrl  // Use adaptive card compatible URL
                                }
                            ]
                        }
                    ]
                };

                const adaptiveCardPayload = {
                    body: {
                        contentType: 'html',
                        content: `<attachment id="${driveItem.id}"></attachment>`
                    },
                    attachments: [
                        {
                            id: driveItem.id,
                            contentType: 'application/vnd.microsoft.card.adaptive',
                            content: JSON.stringify(adaptiveCard)
                        }
                    ]
                };

                const messageResponse = await messageDelegatedGraphClient.api(`/chats/${chatId}/messages`)
                    .post(adaptiveCardPayload);
                console.log('[server.js] Voice message sent as adaptive card (primary):', messageResponse.id);

            } catch (adaptiveCardError) {
                console.error('[server.js] Adaptive card failed:', adaptiveCardError);
                throw new Error(`Failed to send voice message: ${adaptiveCardError.message}`);
            }
        } else {
            console.log('[server.js] No chatId provided; file uploaded to OneDrive or local storage');
        }
 
        return {
            id: driveItem ? driveItem.id : null,
            downloadUrl: audioUrl,
            fileName: fileName
        };
    } catch (error) {
        console.error('[server.js] Error in uploadFileToTeams:', error);

        // Provide specific error messages for different failure types
        if (error.message.includes('FFmpeg') || error.message.includes('conversion')) {
            throw new Error('Audio conversion failed. Please try recording your voice message again.');
        } else if (error.message.includes('OneDrive') || error.message.includes('drive') || error instanceof GraphError) {
            if (error instanceof GraphError) {
                console.error('[server.js] Graph API Error Details:', {
                    statusCode: error.statusCode,
                    code: error.code,
                    message: error.message
                });
            }
            throw new Error('Failed to save voice message to OneDrive. Please check your connection and try again.');
        } else if (error.message.includes('sharing') || error.message.includes('link')) {
            throw new Error('Failed to create secure sharing link. Please try again.');
        } else if (error.message.includes('timeout')) {
            throw new Error('Upload timed out. Please try again with a shorter voice message.');
        } else {
            throw new Error(`Voice message processing failed: ${error.message}`);
        }
    } finally {
        if (tempMp3Path) {
            await fsPromises.unlink(tempMp3Path).catch(err => 
                console.error('[server.js] Failed to delete temp file:', err)
            );
        }
    }
}
 
io.on('connection', (socket) => {
    console.log('[server.js] Client connected via Socket.IO:', socket.id, socket.handshake.query);
    let userContext = null;
 
    socket.on('initialize-context', async (context) => {
        console.log('[server.js] Context initialized:', context);
        userContext = context;
        try {
            await fetchAccessToken();
        } catch (error) {
            console.error('[server.js] Failed to initialize context with access token:', error);
            socket.emit('upload-error', { error: 'Server initialization failed', details: error.message });
        }
    });
 
    socket.on('upload-voice-message', async (data) => {
        try {
            console.log('[server.js] Received voice message request:', {
                chatType: data.chatType,
                channelId: data.channelId,
                recipientId: data.recipientId,
                senderId: data.senderId,
                blobSize: data.audioBlob?.length || 'unknown'
            });

            // Ensure we have user context before proceeding
            if (!userContext) {
                throw new Error('User context not initialized. Please try again.');
            }

            const { audioBlob, recipientId, senderId, chatType, channelId, delegatedToken } = data;

            // SECURITY: Validate delegated token before processing
            if (!delegatedToken) {
                throw new Error('Authentication required: No delegated token provided');
            }

            // Validate the delegated token by testing Graph API access
            try {
                const testGraphClient = Client.init({
                    authProvider: (done) => {
                        done(null, delegatedToken);
                    }
                });

                const userProfile = await testGraphClient.api('/me').get();
                console.log(`[server.js] Socket.IO request authenticated for user: ${userProfile.userPrincipalName}`);

                // Verify the authenticated user matches the sender
                if (userProfile.id !== senderId) {
                    console.error(`[server.js] SECURITY VIOLATION: Authenticated user ${userProfile.id} does not match sender ${senderId}`);
                    throw new Error('Authentication error: User mismatch');
                }
            } catch (authError) {
                console.error('[server.js] Socket.IO authentication failed:', authError.message);
                throw new Error(`Authentication failed: ${authError.message}`);
            }
            
            // Initialize chatId based on channelId or create new chat
            let chatId = channelId || null;
            if (!chatId && chatType === 'oneOnOne' && recipientId && senderId !== recipientId) {
                const chat = await createOrGetChat(senderId, recipientId);
                chatId = chat.id;
            }

            if (!chatId) {
                throw new Error('Chat ID is required for sending messages');
            }

            if (!audioBlob || !senderId) {
                throw new Error('Missing required fields: audioBlob or senderId');
            }

            const messageId = uuidv4();
            const fileName = `voice-message-${messageId}.webm`;
            const buffer = Buffer.from(audioBlob, 'base64');

            // Add timeout handler to prevent app from getting stuck
            const timeoutId = setTimeout(() => {
                console.error('[server.js] Voice message processing timeout after 60 seconds');
                socket.emit('upload-error', {
                    error: 'Voice message processing timed out. The app will close automatically.',
                    details: 'Operation took too long to complete (60 seconds)',
                    shouldClose: true
                });

                // Close the app after showing timeout error
                setTimeout(() => {
                    socket.emit('closeApp', {
                        reason: 'Processing timeout - operation exceeded time limit',
                        error: 'Timeout after 60 seconds'
                    });
                }, 2000);
            }, 60000); // 60 second timeout

            const uploadedFile = await uploadFileToTeams(chatId, fileName, buffer, userContext, delegatedToken, data.preFetchedData);
            console.log('[server.js] File uploaded successfully:', uploadedFile);

            clearTimeout(timeoutId); // Clear timeout on success

            // Ensure we have all required data before emitting
            const successData = {
                messageId,
                downloadUrl: uploadedFile.downloadUrl,
                fileName: uploadedFile.fileName,
                chatId: chatId,
                userId: userContext?.userObjectId || 'unknown',
                userName: userContext?.userDisplayName || 'Unknown User'
            };
            // Emit immediately and verify
            socket.emit('upload-success', successData);

            // Double check that socket is still connected
            if (!socket.connected) {
                console.error('[server.js] Socket disconnected before success could be emitted');
                throw new Error('Socket connection lost');
            }
        } catch (error) {
            console.error('[server.js] Error handling voice message:', error);

            // Send error to client with app closure instruction
            socket.emit('upload-error', {
                error: 'Failed to send voice message. The app will close automatically.',
                details: error.message,
                shouldClose: true
            });

            // Close the app after showing error message
            setTimeout(() => {
                socket.emit('closeApp', {
                    reason: 'Critical error occurred during voice message processing',
                    error: error.message
                });
            }, 2000);
        }
    });



    socket.on('disconnect', (reason) => {
        console.log('[server.js] Client disconnected from Socket.IO:', socket.id, reason);
    });
 
    socket.on('error', (error) => {
        console.error('[server.js] Socket.IO error:', error);
    });
});
 
// Add cleanup interval
setInterval(() => {
    global.gc && global.gc();  // Run garbage collection if available
}, 300000);  // Every 5 minutes
 
// Global error handlers to prevent app crashes
process.on('unhandledRejection', (reason, promise) => {
    console.error('[server.js] Unhandled Promise Rejection:', reason);
    console.error('[server.js] Promise:', promise);

    // Emit error to all connected clients
    io.emit('upload-error', {
        error: 'A critical error occurred. The app will close automatically.',
        details: 'Unhandled promise rejection',
        shouldClose: true
    });

    setTimeout(() => {
        io.emit('closeApp', {
            reason: 'Critical system error - unhandled promise rejection',
            error: reason?.message || 'Unknown error'
        });
    }, 2000);
});

process.on('uncaughtException', (error) => {
    console.error('[server.js] Uncaught Exception:', error);

    // Emit error to all connected clients
    io.emit('upload-error', {
        error: 'A critical system error occurred. The app will close automatically.',
        details: 'Uncaught exception',
        shouldClose: true
    });

    setTimeout(() => {
        io.emit('closeApp', {
            reason: 'Critical system error - uncaught exception',
            error: error.message
        });

        // Exit the process after notifying clients
        setTimeout(() => {
            process.exit(1);
        }, 3000);
    }, 2000);
});

const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
    console.log(`[server.js] Server running on https://localhost:${PORT}`);
});