<!DOCTYPE html>
<html>
<head>
    <title>Authentication Complete</title>
    <script src="https://res.cdn.office.net/teams-js/2.0.0/js/MicrosoftTeams.min.js"></script>
</head>
<body>
    <p>Authentication in progress...</p>
    <script>
        console.log('[auth-end.html] Script loaded at', new Date().toISOString());
        if (typeof microsoftTeams !== 'undefined') {
            try {
                microsoftTeams.initialize(() => {
                    console.log('[auth-end.html] Microsoft Teams SDK initialized');
                    // Extract token from URL fragment
                    const hash = window.location.hash;
                    const params = new URLSearchParams(hash.replace('#', ''));
                    const accessToken = params.get('access_token');
                    const error = params.get('error_description') || params.get('error');

                    if (accessToken) {
                        console.log('[auth-end.html] Access token retrieved:', accessToken.substring(0, 20) + '...');
                        microsoftTeams.authentication.notifySuccess(accessToken);
                    } else if (error) {
                        console.error('[auth-end.html] Authentication error:', error);
                        microsoftTeams.authentication.notifyFailure(error);
                    } else {
                        console.error('[auth-end.html] No token or error in URL');
                        microsoftTeams.authentication.notifyFailure('No token received');
                    }
                });
            } catch (error) {
                console.error('[auth-end.html] SDK initialization failed:', error);
                microsoftTeams.authentication.notifyFailure('SDK initialization failed: ' + error.message);
            }
        } else {
            console.error('[auth-end.html] Microsoft Teams SDK not loaded');
            microsoftTeams.authentication.notifyFailure('Teams SDK not loaded');
        }
    </script>
</body>
</html>