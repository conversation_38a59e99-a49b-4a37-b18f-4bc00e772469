.recorder-container {
    padding: 2rem;
    border-radius: 8px;
    background-color: #f8f9fa;
}

#chatSelect {
    width: 100%;
    margin-bottom: 1rem;
}

#chatSelect option {
    padding: 8px;
    cursor: pointer;
}

#chatSelect option:hover {
    background-color: #f8f9fa;
}

#chatSearch {
    margin-bottom: 1rem;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #ced4da;
}

.chat-selector {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 2rem;
}

#recordButton {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    transition: all 0.3s ease;
}

#recordButton i {
    font-size: 3rem;
    margin-bottom: 0.5rem;
}

#recordButton.recording {
    background-color: #dc3545;
    border-color: #dc3545;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

#recordingStatus {
    color: #dc3545;
    font-weight: bold;
}

#timer {
    font-family: monospace;
}

#previewContainer {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
}

.alert {
    margin-top: 1rem;
}

/* Teams dark theme support */
body.theme-dark {
    background-color: #201f1f;
    color: #ffffff;
}

body.theme-dark .card {
    background-color: #2d2c2c;
    border-color: #444444;
}

body.theme-dark .recorder-container,
body.theme-dark .chat-selector,
body.theme-dark #previewContainer {
    background-color: #3d3d3d;
}

body.theme-dark #chatSelect,
body.theme-dark #chatSearch {
    background-color: #2d2c2c;
    color: #ffffff;
    border-color: #444444;
}

body.theme-dark #chatSelect option {
    background-color: #2d2c2c;
}

body.theme-dark #chatSelect option:hover {
    background-color: #3d3d3d;
} 